-- Migration to move agent_configs data to agents.config column
-- This migration consolidates all agent configurations into a single JSON column

-- First, update agents table with consolidated config data
UPDATE agents 
SET config = (
  SELECT jsonb_object_agg(
    ac.config_type, 
    jsonb_build_object(
      'config_type', ac.config_type,
      'provider', ac.provider,
      'external_provider_id', ac.external_provider_id,
      'config', ac.config,
      'custom_metadata', ac.custom_metadata
    )
  )
  FROM agent_configs ac 
  WHERE ac.agent_id = agents.id
)
WHERE id IN (SELECT DISTINCT agent_id FROM agent_configs WHERE agent_id IS NOT NULL);

-- Drop the agent_configs table and related constraints
DROP TABLE IF EXISTS agent_configs CASCADE;

-- Update the agents table to ensure config column has proper default
ALTER TABLE agents 
ALTER COLUMN config SET DEFAULT '{}'::jsonb;

-- Add a check constraint to ensure config is valid JSON
ALTER TABLE agents 
ADD CONSTRAINT agents_config_is_object 
CHECK (config IS NULL OR jsonb_typeof(config) = 'object');

-- Create an index on the config column for better performance
CREATE INDEX IF NOT EXISTS idx_agents_config_gin ON agents USING gin (config);

-- Create a function to get specific config type from agent
CREATE OR REPLACE FUNCTION get_agent_config(agent_id uuid, config_type text)
RETURNS jsonb
LANGUAGE sql
STABLE
AS $$
  SELECT config->config_type FROM agents WHERE id = agent_id;
$$;

-- Create a function to update specific config type for an agent
CREATE OR REPLACE FUNCTION update_agent_config(
  agent_id uuid, 
  config_type text, 
  new_config jsonb
)
RETURNS void
LANGUAGE sql
AS $$
  UPDATE agents 
  SET config = COALESCE(config, '{}'::jsonb) || jsonb_build_object(config_type, new_config)
  WHERE id = agent_id;
$$;

-- Create a function to remove specific config type from an agent
CREATE OR REPLACE FUNCTION remove_agent_config(agent_id uuid, config_type text)
RETURNS void
LANGUAGE sql
AS $$
  UPDATE agents 
  SET config = config - config_type
  WHERE id = agent_id;
$$;
